{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=KnoarkLeadGenDb;Trusted_Connection=True;MultipleActiveResultSets=true", "HangfireConnection": "Server=(localdb)\\mssqllocaldb;Database=KnoarkLeadGenHangfireDb;Trusted_Connection=True;MultipleActiveResultSets=true"}, "Email": {"From": "<EMAIL>", "Smtp": {"Host": "smtp.gmail.com", "Port": "587", "Username": "<EMAIL>", "Password": "your-app-password"}}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}