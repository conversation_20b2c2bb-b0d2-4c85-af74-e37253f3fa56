using System.ComponentModel.DataAnnotations;

namespace KnoarkLeadGen.Data
{
    public class Lead
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(200)]
        public string CompanyName { get; set; }

        [MaxLength(500)]
        public string Website { get; set; }

        [MaxLength(100)]
        public string Industry { get; set; }

        [MaxLength(100)]
        public string ProductType { get; set; }

        [MaxLength(100)]
        public string ContactName { get; set; }

        [MaxLength(100)]
        public string JobTitle { get; set; }

        [EmailAddress]
        [MaxLength(200)]
        public string Email { get; set; }

        [MaxLength(200)]
        public string Location { get; set; }

        [MaxLength(500)]
        public string Notes { get; set; }

        public LeadStatus Status { get; set; } = LeadStatus.New;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? UpdatedAt { get; set; }
    }

    public enum LeadStatus
    {
        New,
        Contacted,
        Replied
    }
}
