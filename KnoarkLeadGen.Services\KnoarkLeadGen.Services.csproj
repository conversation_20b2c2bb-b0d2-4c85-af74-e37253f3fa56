<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Hangfire.Core" Version="1.8.21" />
    <PackageReference Include="HtmlAgilityPack" Version="1.12.4" />
    <PackageReference Include="MailKit" Version="4.14.1" />
    <PackageReference Include="Microsoft.Playwright" Version="1.55.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\KnoarkLeadGen.Data\KnoarkLeadGen.Data.csproj" />
  </ItemGroup>

</Project>
