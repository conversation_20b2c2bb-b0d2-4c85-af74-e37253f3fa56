.page[b-h99kre177h] {
    position: relative;
    display: flex;
    flex-direction: column;
}

main[b-h99kre177h] {
    flex: 1;
}

.sidebar[b-h99kre177h] {
    background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
}

.top-row[b-h99kre177h] {
    background-color: #f7f7f7;
    border-bottom: 1px solid #d6d5d5;
    justify-content: flex-end;
    height: 3.5rem;
    display: flex;
    align-items: center;
}

    .top-row[b-h99kre177h]  a, .top-row .btn-link[b-h99kre177h] {
        white-space: nowrap;
        margin-left: 1.5rem;
    }

    .top-row a:first-child[b-h99kre177h] {
        overflow: hidden;
        text-overflow: ellipsis;
    }

@media (max-width: 640.98px) {
    .top-row:not(.auth)[b-h99kre177h] {
        display: none;
    }

    .top-row.auth[b-h99kre177h] {
        justify-content: space-between;
    }

    .top-row a[b-h99kre177h], .top-row .btn-link[b-h99kre177h] {
        margin-left: 0;
    }
}

@media (min-width: 641px) {
    .page[b-h99kre177h] {
        flex-direction: row;
    }

    .sidebar[b-h99kre177h] {
        width: 250px;
        height: 100vh;
        position: sticky;
        top: 0;
    }

    .top-row[b-h99kre177h] {
        position: sticky;
        top: 0;
        z-index: 1;
    }

    .top-row[b-h99kre177h], article[b-h99kre177h] {
        padding-left: 2rem !important;
        padding-right: 1.5rem !important;
    }
}
