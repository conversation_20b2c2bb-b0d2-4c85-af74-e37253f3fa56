C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\obj\Debug\net8.0\KnoarkLeadGen.Services.csproj.AssemblyReference.cache
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\obj\Debug\net8.0\KnoarkLeadGen.Services.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\obj\Debug\net8.0\KnoarkLeadGen.Services.AssemblyInfoInputs.cache
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\obj\Debug\net8.0\KnoarkLeadGen.Services.AssemblyInfo.cs
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\obj\Debug\net8.0\KnoarkLeadGen.Services.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\node\win32_x64\node.exe
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\node\LICENSE
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\api.json
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\bin\install_media_pack.ps1
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\bin\reinstall_chrome_beta_linux.sh
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\bin\reinstall_chrome_beta_mac.sh
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\bin\reinstall_chrome_beta_win.ps1
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\bin\reinstall_chrome_stable_linux.sh
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\bin\reinstall_chrome_stable_mac.sh
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\bin\reinstall_chrome_stable_win.ps1
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\bin\reinstall_msedge_beta_linux.sh
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\bin\reinstall_msedge_beta_mac.sh
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\bin\reinstall_msedge_beta_win.ps1
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\bin\reinstall_msedge_dev_linux.sh
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\bin\reinstall_msedge_dev_mac.sh
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\bin\reinstall_msedge_dev_win.ps1
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\bin\reinstall_msedge_stable_linux.sh
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\bin\reinstall_msedge_stable_mac.sh
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\bin\reinstall_msedge_stable_win.ps1
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\browsers.json
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\cli.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\index.d.ts
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\index.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\index.mjs
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\androidServerImpl.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\browserServerImpl.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\accessibility.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\android.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\api.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\artifact.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\browser.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\browserContext.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\browserType.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\cdpSession.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\channelOwner.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\clientHelper.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\clientInstrumentation.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\clientStackTrace.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\clock.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\connection.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\consoleMessage.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\coverage.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\dialog.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\download.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\electron.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\elementHandle.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\errors.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\eventEmitter.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\events.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\fetch.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\fileChooser.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\fileUtils.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\frame.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\harRouter.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\input.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\jsHandle.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\jsonPipe.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\localUtils.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\locator.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\network.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\page.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\platform.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\playwright.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\selectors.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\stream.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\timeoutSettings.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\tracing.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\types.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\video.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\waiter.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\webError.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\webSocket.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\worker.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\client\writableStream.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\cli\driver.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\cli\program.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\cli\programWithTestStub.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\generated\bindingsControllerSource.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\generated\clockSource.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\generated\injectedScriptSource.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\generated\pollingRecorderSource.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\generated\storageScriptSource.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\generated\utilityScriptSource.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\generated\webSocketMockSource.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\inprocess.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\inProcessFactory.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\outofprocess.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\protocol\serializers.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\protocol\validator.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\protocol\validatorPrimitives.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\remote\playwrightConnection.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\remote\playwrightServer.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\accessibility.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\android\android.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\android\backendAdb.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\artifact.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\bidi\bidiBrowser.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\bidi\bidiChromium.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\bidi\bidiConnection.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\bidi\bidiExecutionContext.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\bidi\bidiFirefox.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\bidi\bidiInput.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\bidi\bidiNetworkManager.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\bidi\bidiOverCdp.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\bidi\bidiPage.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\bidi\bidiPdf.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\bidi\third_party\bidiCommands.d.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\bidi\third_party\bidiDeserializer.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\bidi\third_party\bidiKeyboard.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\bidi\third_party\bidiProtocol.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\bidi\third_party\bidiProtocolCore.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\bidi\third_party\bidiProtocolPermissions.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\bidi\third_party\bidiSerializer.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\bidi\third_party\firefoxPrefs.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\browser.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\browserContext.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\browserType.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\callLog.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\chromium\appIcon.png
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\chromium\chromium.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\chromium\chromiumSwitches.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\chromium\crAccessibility.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\chromium\crBrowser.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\chromium\crConnection.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\chromium\crCoverage.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\chromium\crDevTools.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\chromium\crDragDrop.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\chromium\crExecutionContext.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\chromium\crInput.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\chromium\crNetworkManager.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\chromium\crPage.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\chromium\crPdf.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\chromium\crProtocolHelper.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\chromium\crServiceWorker.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\chromium\defaultFontFamilies.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\chromium\protocol.d.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\chromium\videoRecorder.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\clock.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\codegen\csharp.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\codegen\java.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\codegen\javascript.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\codegen\jsonl.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\codegen\language.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\codegen\languages.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\codegen\python.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\codegen\types.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\console.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\cookieStore.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\debugController.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\debugger.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\deviceDescriptors.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\deviceDescriptorsSource.json
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\dialog.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\androidDispatcher.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\artifactDispatcher.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\browserContextDispatcher.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\browserDispatcher.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\browserTypeDispatcher.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\cdpSessionDispatcher.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\debugControllerDispatcher.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\dialogDispatcher.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\dispatcher.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\electronDispatcher.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\elementHandlerDispatcher.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\frameDispatcher.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\jsHandleDispatcher.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\jsonPipeDispatcher.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\localUtilsDispatcher.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\networkDispatchers.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\pageDispatcher.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\playwrightDispatcher.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\streamDispatcher.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\tracingDispatcher.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\webSocketRouteDispatcher.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\dispatchers\writableStreamDispatcher.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\dom.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\download.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\electron\electron.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\electron\loader.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\errors.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\fetch.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\fileChooser.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\fileUploadUtils.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\firefox\ffAccessibility.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\firefox\ffBrowser.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\firefox\ffConnection.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\firefox\ffExecutionContext.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\firefox\ffInput.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\firefox\ffNetworkManager.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\firefox\ffPage.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\firefox\firefox.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\firefox\protocol.d.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\formData.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\frames.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\frameSelectors.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\harBackend.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\har\harRecorder.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\har\harTracer.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\helper.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\index.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\input.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\instrumentation.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\javascript.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\launchApp.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\localUtils.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\macEditingCommands.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\network.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\page.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\pipeTransport.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\playwright.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\progress.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\protocolError.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\recorder.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\recorder\chat.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\recorder\recorderApp.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\recorder\recorderRunner.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\recorder\recorderSignalProcessor.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\recorder\recorderUtils.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\recorder\throttledFile.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\registry\browserFetcher.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\registry\dependencies.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\registry\index.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\registry\nativeDeps.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\registry\oopDownloadBrowserMain.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\screenshotter.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\selectors.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\socksClientCertificatesInterceptor.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\socksInterceptor.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\trace\recorder\snapshotter.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\trace\recorder\snapshotterInjected.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\trace\recorder\tracing.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\trace\test\inMemorySnapshotter.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\trace\viewer\traceViewer.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\transport.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\types.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\usKeyboardLayout.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\utils\ascii.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\utils\comparators.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\utils\crypto.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\utils\debug.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\utils\debugLogger.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\utils\env.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\utils\eventsHelper.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\utils\expectUtils.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\utils\fileUtils.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\utils\happyEyeballs.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\utils\hostPlatform.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\utils\httpServer.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\utils\image_tools\colorUtils.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\utils\image_tools\compare.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\utils\image_tools\imageChannel.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\utils\image_tools\stats.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\utils\linuxUtils.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\utils\network.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\utils\nodePlatform.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\utils\pipeTransport.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\utils\processLauncher.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\utils\profiler.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\utils\socksProxy.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\utils\spawnAsync.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\utils\task.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\utils\userAgent.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\utils\wsServer.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\utils\zipFile.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\utils\zones.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\webkit\protocol.d.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\webkit\webkit.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\webkit\wkAccessibility.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\webkit\wkBrowser.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\webkit\wkConnection.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\webkit\wkExecutionContext.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\webkit\wkInput.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\webkit\wkInterceptableRequest.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\webkit\wkPage.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\webkit\wkProvisionalPage.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\server\webkit\wkWorkers.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\third_party\pixelmatch.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\utils.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\utilsBundle.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\utilsBundleImpl\index.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\utilsBundleImpl\xdg-open
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\utils\isomorphic\ariaSnapshot.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\utils\isomorphic\assert.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\utils\isomorphic\colors.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\utils\isomorphic\cssParser.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\utils\isomorphic\cssTokenizer.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\utils\isomorphic\headers.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\utils\isomorphic\locatorGenerators.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\utils\isomorphic\locatorParser.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\utils\isomorphic\locatorUtils.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\utils\isomorphic\manualPromise.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\utils\isomorphic\mimeType.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\utils\isomorphic\multimap.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\utils\isomorphic\protocolFormatter.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\utils\isomorphic\protocolMetainfo.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\utils\isomorphic\rtti.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\utils\isomorphic\selectorParser.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\utils\isomorphic\semaphore.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\utils\isomorphic\stackTrace.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\utils\isomorphic\stringUtils.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\utils\isomorphic\time.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\utils\isomorphic\timeoutRunner.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\utils\isomorphic\traceUtils.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\utils\isomorphic\types.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\utils\isomorphic\urlMatch.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\utils\isomorphic\utilityScriptSerializers.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\vite\htmlReport\index.html
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\vite\recorder\assets\codeMirrorModule-C3UTv-Ge.css
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\vite\recorder\assets\codeMirrorModule-DzQ0k89p.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\vite\recorder\assets\codicon-DCmgc-ay.ttf
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\vite\recorder\assets\index-CI4HQ-Zb.css
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\vite\recorder\assets\index-D7C7daHH.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\vite\recorder\index.html
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\vite\recorder\playwright-logo.svg
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\vite\traceViewer\assets\codeMirrorModule-CEFqZ5b3.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\vite\traceViewer\assets\defaultSettingsView-BA25Usqk.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\vite\traceViewer\assets\xtermModule-BoAIEibi.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\vite\traceViewer\codeMirrorModule.C3UTv-Ge.css
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\vite\traceViewer\codicon.DCmgc-ay.ttf
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\vite\traceViewer\defaultSettingsView.DVJHpiGt.css
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\vite\traceViewer\index.BFsek2M6.css
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\vite\traceViewer\index.CheexZ4_.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\vite\traceViewer\index.html
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\vite\traceViewer\playwright-logo.svg
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\vite\traceViewer\snapshot.html
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\vite\traceViewer\sw.bundle.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\vite\traceViewer\uiMode.BatfzHMG.css
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\vite\traceViewer\uiMode.Dy4dnPNW.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\vite\traceViewer\uiMode.html
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\vite\traceViewer\xtermModule.Beg8tuEN.css
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\zipBundle.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\lib\zipBundleImpl.js
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\LICENSE
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\NOTICE
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\package.json
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\protocol.yml
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\README.md
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\ThirdPartyNotices.txt
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\types\protocol.d.ts
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\types\structs.d.ts
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\.playwright\package\types\types.d.ts
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\playwright.ps1
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\KnoarkLeadGen.Services.deps.json
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\KnoarkLeadGen.Services.dll
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\KnoarkLeadGen.Services.pdb
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\KnoarkLeadGen.Data.dll
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\bin\Debug\net8.0\KnoarkLeadGen.Data.pdb
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\obj\Debug\net8.0\KnoarkLe.D07B6A89.Up2Date
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\obj\Debug\net8.0\KnoarkLeadGen.Services.dll
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\obj\Debug\net8.0\refint\KnoarkLeadGen.Services.dll
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\obj\Debug\net8.0\KnoarkLeadGen.Services.pdb
C:\Users\<USER>\Desktop\KnoarkLeadGen\KnoarkLeadGen.Services\obj\Debug\net8.0\ref\KnoarkLeadGen.Services.dll
