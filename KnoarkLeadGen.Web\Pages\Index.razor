﻿@page "/"

@using KnoarkLeadGen.Data
@using KnoarkLeadGen.Services
@using Microsoft.EntityFrameworkCore
@using Microsoft.JSInterop
@inject ApplicationDbContext DbContext
@inject IScrapingService ScrapingService
@inject IEmailService EmailService
@inject ILogger<Index> Logger
@inject IJSRuntime JS

<PageTitle>Lead Management Dashboard</PageTitle>

<h1>Lead Management Dashboard</h1>

<div class="row">
    <div class="col-md-12">
        <button class="btn btn-primary mb-3" @onclick="ScrapeLeads">Scrape New Leads</button>
        <button class="btn btn-success mb-3 ms-2" @onclick="ExportToCsv">Export to CSV</button>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Company Name</th>
                    <th>Website</th>
                    <th>Industry</th>
                    <th>Contact Name</th>
                    <th>Job Title</th>
                    <th>Email</th>
                    <th>Location</th>
                    <th>Notes</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var lead in leads)
                {
                    <tr>
                        <td>@lead.CompanyName</td>
                        <td><a href="@lead.Website" target="_blank">@lead.Website</a></td>
                        <td>@lead.Industry</td>
                        <td>@lead.ContactName</td>
                        <td>@lead.JobTitle</td>
                        <td>@lead.Email</td>
                        <td>@lead.Location</td>
                        <td>@lead.Notes</td>
                        <td>@lead.Status</td>
                        <td>
                            @if (lead.Status == LeadStatus.New)
                            {
                                <button class="btn btn-sm btn-warning" @onclick="() => SendEmail(lead)">Send Email</button>
                            }
                            @if (lead.Status == LeadStatus.Contacted)
                            {
                                <button class="btn btn-sm btn-info" @onclick="() => MarkAsReplied(lead)">Mark as
                                    Replied</button>
                            }
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
</div>

@code {
    private List<Lead> leads = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadLeads();
    }

    private async Task LoadLeads()
    {
        leads = await DbContext.Leads.OrderByDescending(l => l.CreatedAt).ToListAsync();
        StateHasChanged();
    }

    private async Task ScrapeLeads()
    {
        try
        {
            var newLeads = await ScrapingService.ScrapeLeadsAsync();
            foreach (var lead in newLeads)
            {
                DbContext.Leads.Add(lead);
            }
            await DbContext.SaveChangesAsync();
            await LoadLeads();
            Logger.LogInformation($"Scraped and saved {newLeads.Count} new leads.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error scraping leads.");
        }
    }

    private async Task SendEmail(Lead lead)
    {
        try
        {
            await EmailService.SendLeadEmailAsync(lead);
            lead.Status = LeadStatus.Contacted;
            lead.UpdatedAt = DateTime.UtcNow;
            await DbContext.SaveChangesAsync();
            await LoadLeads();
            Logger.LogInformation($"Email sent to {lead.Email}.");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, $"Error sending email to {lead.Email}.");
        }
    }

    private async Task MarkAsReplied(Lead lead)
    {
        lead.Status = LeadStatus.Replied;
        lead.UpdatedAt = DateTime.UtcNow;
        await DbContext.SaveChangesAsync();
        await LoadLeads();
    }

    private async Task ExportToCsv()
    {
        var csv = "Company Name,Website,Industry,Contact Name,Job Title,Email,Location,Notes,Status,Created At\n";
        foreach (var lead in leads)
        {
            csv +=
            $"{lead.CompanyName},{lead.Website},{lead.Industry},{lead.ContactName},{lead.JobTitle},{lead.Email},{lead.Location},{lead.Notes},{lead.Status},{lead.CreatedAt}\n";
        }

        var bytes = System.Text.Encoding.UTF8.GetBytes(csv);
        var file = new MemoryStream(bytes);
        var fileName = $"leads_{DateTime.Now:yyyyMMdd_HHmmss}.csv";

        await JS.InvokeVoidAsync("downloadFile", fileName, Convert.ToBase64String(bytes));
    }
}