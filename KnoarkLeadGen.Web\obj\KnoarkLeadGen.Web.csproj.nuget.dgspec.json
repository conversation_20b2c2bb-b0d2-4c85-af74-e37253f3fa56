{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\KnoarkLeadGen.Web.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Data\\KnoarkLeadGen.Data.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Data\\KnoarkLeadGen.Data.csproj", "projectName": "KnoarkLeadGen.Data", "projectPath": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Data\\KnoarkLeadGen.Data.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Data\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.10, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.10, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.103/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Services\\KnoarkLeadGen.Services.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Services\\KnoarkLeadGen.Services.csproj", "projectName": "KnoarkLeadGen.Services", "projectPath": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Services\\KnoarkLeadGen.Services.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Services\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Data\\KnoarkLeadGen.Data.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Data\\KnoarkLeadGen.Data.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Hangfire.Core": {"target": "Package", "version": "[1.8.21, )"}, "HtmlAgilityPack": {"target": "Package", "version": "[1.12.4, )"}, "MailKit": {"target": "Package", "version": "[4.14.1, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Playwright": {"target": "Package", "version": "[1.55.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.103/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\KnoarkLeadGen.Web.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\KnoarkLeadGen.Web.csproj", "projectName": "KnoarkLeadGen.Web", "projectPath": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\KnoarkLeadGen.Web.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Data\\KnoarkLeadGen.Data.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Data\\KnoarkLeadGen.Data.csproj"}, "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Services\\KnoarkLeadGen.Services.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Services\\KnoarkLeadGen.Services.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Blazored.TextEditor": {"target": "Package", "version": "[1.0.8, )"}, "ClosedXML": {"target": "Package", "version": "[0.102.3, )"}, "Hangfire.AspNetCore": {"target": "Package", "version": "[1.8.21, )"}, "Hangfire.SqlServer": {"target": "Package", "version": "[1.8.21, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.10, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.103/PortableRuntimeIdentifierGraph.json"}}}}}