{"ContentRoots": ["C:\\Users\\<USER>\\.nuget\\packages\\blazored.texteditor\\1.0.8\\staticwebassets\\", "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\", "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\obj\\Debug\\net8.0\\scopedcss\\bundle\\"], "Root": {"Children": {"_content": {"Children": {"Blazored.TextEditor": {"Children": {"Blazored-BlazorQuill.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "Blazored-BlazorQuill.js"}, "Patterns": null}, "quill-blot-formatter.min.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "quill-blot-formatter.min.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "css": {"Children": {"bootstrap": {"Children": {"bootstrap.min.css": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "css/bootstrap/bootstrap.min.css"}, "Patterns": null}, "bootstrap.min.css.map": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "css/bootstrap/bootstrap.min.css.map"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "open-iconic": {"Children": {"FONT-LICENSE": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "css/open-iconic/FONT-LICENSE"}, "Patterns": null}, "font": {"Children": {"css": {"Children": {"open-iconic-bootstrap.min.css": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "css/open-iconic/font/css/open-iconic-bootstrap.min.css"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "fonts": {"Children": {"open-iconic.eot": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "css/open-iconic/font/fonts/open-iconic.eot"}, "Patterns": null}, "open-iconic.otf": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "css/open-iconic/font/fonts/open-iconic.otf"}, "Patterns": null}, "open-iconic.svg": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "css/open-iconic/font/fonts/open-iconic.svg"}, "Patterns": null}, "open-iconic.ttf": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "css/open-iconic/font/fonts/open-iconic.ttf"}, "Patterns": null}, "open-iconic.woff": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "css/open-iconic/font/fonts/open-iconic.woff"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "ICON-LICENSE": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "css/open-iconic/ICON-LICENSE"}, "Patterns": null}, "README.md": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "css/open-iconic/README.md"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "site.css": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "css/site.css"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "favicon.ico": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "favicon.ico"}, "Patterns": null}, "KnoarkLeadGen.Web.styles.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "KnoarkLeadGen.Web.styles.css"}, "Patterns": null}}, "Asset": null, "Patterns": [{"ContentRootIndex": 1, "Pattern": "**", "Depth": 0}]}}