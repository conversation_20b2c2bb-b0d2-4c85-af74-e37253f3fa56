/* _content/KnoarkLeadGen.Web/Shared/MainLayout.razor.rz.scp.css */
.page[b-h99kre177h] {
    position: relative;
    display: flex;
    flex-direction: column;
}

main[b-h99kre177h] {
    flex: 1;
}

.sidebar[b-h99kre177h] {
    background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
}

.top-row[b-h99kre177h] {
    background-color: #f7f7f7;
    border-bottom: 1px solid #d6d5d5;
    justify-content: flex-end;
    height: 3.5rem;
    display: flex;
    align-items: center;
}

    .top-row[b-h99kre177h]  a, .top-row .btn-link[b-h99kre177h] {
        white-space: nowrap;
        margin-left: 1.5rem;
    }

    .top-row a:first-child[b-h99kre177h] {
        overflow: hidden;
        text-overflow: ellipsis;
    }

@media (max-width: 640.98px) {
    .top-row:not(.auth)[b-h99kre177h] {
        display: none;
    }

    .top-row.auth[b-h99kre177h] {
        justify-content: space-between;
    }

    .top-row a[b-h99kre177h], .top-row .btn-link[b-h99kre177h] {
        margin-left: 0;
    }
}

@media (min-width: 641px) {
    .page[b-h99kre177h] {
        flex-direction: row;
    }

    .sidebar[b-h99kre177h] {
        width: 250px;
        height: 100vh;
        position: sticky;
        top: 0;
    }

    .top-row[b-h99kre177h] {
        position: sticky;
        top: 0;
        z-index: 1;
    }

    .top-row[b-h99kre177h], article[b-h99kre177h] {
        padding-left: 2rem !important;
        padding-right: 1.5rem !important;
    }
}
/* _content/KnoarkLeadGen.Web/Shared/NavMenu.razor.rz.scp.css */
.navbar-toggler[b-7hcxl2cu8f] {
    background-color: rgba(255, 255, 255, 0.1);
}

.top-row[b-7hcxl2cu8f] {
    height: 3.5rem;
    background-color: rgba(0,0,0,0.4);
}

.navbar-brand[b-7hcxl2cu8f] {
    font-size: 1.1rem;
}

.oi[b-7hcxl2cu8f] {
    width: 2rem;
    font-size: 1.1rem;
    vertical-align: text-top;
    top: -2px;
}

.nav-item[b-7hcxl2cu8f] {
    font-size: 0.9rem;
    padding-bottom: 0.5rem;
}

    .nav-item:first-of-type[b-7hcxl2cu8f] {
        padding-top: 1rem;
    }

    .nav-item:last-of-type[b-7hcxl2cu8f] {
        padding-bottom: 1rem;
    }

    .nav-item[b-7hcxl2cu8f]  a {
        color: #d7d7d7;
        border-radius: 4px;
        height: 3rem;
        display: flex;
        align-items: center;
        line-height: 3rem;
    }

.nav-item[b-7hcxl2cu8f]  a.active {
    background-color: rgba(255,255,255,0.25);
    color: white;
}

.nav-item[b-7hcxl2cu8f]  a:hover {
    background-color: rgba(255,255,255,0.1);
    color: white;
}

@media (min-width: 641px) {
    .navbar-toggler[b-7hcxl2cu8f] {
        display: none;
    }

    .collapse[b-7hcxl2cu8f] {
        /* Never collapse the sidebar for wide screens */
        display: block;
    }
}
