﻿@page "/email-campaign"

@using KnoarkLeadGen.Services
@using ClosedXML.Excel
@using System.Collections.Generic
@using Microsoft.AspNetCore.Components.Forms
@using Blazored.TextEditor
@inject IEmailService EmailService
@inject ILogger<EmailCampaign> Logger

<PageTitle>Email Campaign</PageTitle>

<h1>Email Campaign</h1>

<div class="row">
    <div class="col-md-6">
        <h3>Upload Recipients from Excel</h3>
        <InputFile OnChange="HandleFileUpload" accept=".xlsx,.xls" />
        <p class="text-muted">Upload an Excel file with columns: Name, Email</p>
        @if (uploadedRecipients.Count > 0)
        {
            <p>@uploadedRecipients.Count recipients loaded from file.</p>
        }
    </div>
    <div class="col-md-6">
        <h3>Manual Entry</h3>
        <button class="btn btn-secondary mb-2" @onclick="AddManualRecipient">Add Recipient</button>
        @foreach (var recipient in manualRecipients)
        {
            <div class="row mb-2">
                <div class="col-md-5">
                    <input type="text" class="form-control" placeholder="Name" @bind="recipient.Name" />
                </div>
                <div class="col-md-5">
                    <input type="email" class="form-control" placeholder="Email" @bind="recipient.Email" />
                </div>
                <div class="col-md-2">
                    <button class="btn btn-danger" @onclick="() => RemoveManualRecipient(recipient)">Remove</button>
                </div>
            </div>
        }
    </div>
</div>

<hr />

<div class="row">
    <div class="col-md-12">
        <h3>Email Composition</h3>
        <div class="mb-3">
            <label for="subject" class="form-label">Subject</label>
            <input type="text" class="form-control" id="subject" @bind="emailSubject" />
        </div>
        <div class="mb-3">
            <label for="body" class="form-label">Email Body (Rich Text Editor)</label>
            <BlazoredTextEditor @bind-Text="emailBody" />
            <small class="form-text text-muted">Use {{Name}} as placeholder for recipient's name.</small>
        </div>
    </div>
</div>

<hr />

<div class="row">
    <div class="col-md-12">
        <button class="btn btn-primary" @onclick="SendEmails" disabled="@isSending">Send Emails</button>
        @if (isSending)
        {
            <div class="mt-3">
                <div class="progress">
                    <div class="progress-bar" role="progressbar" style="width: @progressPercentage%">@progressPercentage%
                    </div>
                </div>
                <p>Sending emails... @emailsSent / @totalEmails</p>
            </div>
        }
        @if (!string.IsNullOrEmpty(statusMessage))
        {
            <div class="alert alert-info mt-3">@statusMessage</div>
        }
    </div>
</div>

@code {
    private List<(string Name, string Email)> uploadedRecipients = new();
    private List<Recipient> manualRecipients = new();
    private string emailSubject = "";
    private string emailBody = "";
    private bool isSending = false;
    private int progressPercentage = 0;
    private int emailsSent = 0;
    private int totalEmails = 0;
    private string statusMessage = "";

    private class Recipient
    {
        public string Name { get; set; } = "";
        public string Email { get; set; } = "";
    }

    private async Task HandleFileUpload(InputFileChangeEventArgs e)
    {
        uploadedRecipients.Clear();
        var file = e.File;
        if (file != null)
        {
            try
            {
                using var stream = file.OpenReadStream(maxAllowedSize: 10 * 1024 * 1024); // 10MB limit
                using var workbook = new XLWorkbook(stream);
                var worksheet = workbook.Worksheet(1); // First sheet

                foreach (var row in worksheet.RowsUsed().Skip(1)) // Skip header
                {
                    var name = row.Cell(1).GetValue<string>();
                    var email = row.Cell(2).GetValue<string>();
                    if (!string.IsNullOrEmpty(name) && !string.IsNullOrEmpty(email))
                    {
                        uploadedRecipients.Add((name, email));
                    }
                }

                statusMessage = $"Loaded {uploadedRecipients.Count} recipients from Excel file.";
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error parsing Excel file.");
                statusMessage = "Error parsing Excel file. Please ensure it has Name and Email columns.";
                StateHasChanged();
            }
        }
    }

    private void AddManualRecipient()
    {
        manualRecipients.Add(new Recipient());
        StateHasChanged();
    }

    private void RemoveManualRecipient(Recipient recipient)
    {
        manualRecipients.Remove(recipient);
        StateHasChanged();
    }

    private void OnBodyChanged(string value)
    {
        emailBody = value;
    }

    private async Task SendEmails()
    {
        var allRecipients = new List<(string Email, string Name)>();
        allRecipients.AddRange(uploadedRecipients);
        allRecipients.AddRange(manualRecipients.Select(r => (r.Email, r.Name)).Where(r => !string.IsNullOrEmpty(r.Email) &&
        !string.IsNullOrEmpty(r.Name)));

        if (allRecipients.Count == 0)
        {
            statusMessage = "No recipients to send emails to.";
            return;
        }

        if (string.IsNullOrEmpty(emailSubject) || string.IsNullOrEmpty(emailBody))
        {
            statusMessage = "Please provide both subject and body.";
            return;
        }

        isSending = true;
        progressPercentage = 0;
        emailsSent = 0;
        totalEmails = allRecipients.Count;
        statusMessage = "Sending emails...";

        try
        {
            await EmailService.SendBulkEmailsAsync(allRecipients, emailSubject, emailBody);
            statusMessage = $"Emails sent successfully to {totalEmails} recipients.";
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error sending bulk emails.");
            statusMessage = "Error sending emails. Check logs for details.";
        }
        finally
        {
            isSending = false;
            progressPercentage = 100;
            StateHasChanged();
        }
    }
}