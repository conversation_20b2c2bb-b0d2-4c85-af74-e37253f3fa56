{"Files": [{"Id": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\KnoarkLeadGen.Web.bundle.scp.css", "PackagePath": "staticwebassets\\KnoarkLeadGen.Web.gdj14ul9k4.bundle.scp.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "PackagePath": "staticwebassets\\css\\bootstrap\\bootstrap.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "PackagePath": "staticwebassets\\css\\bootstrap\\bootstrap.min.css.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\FONT-LICENSE", "PackagePath": "staticwebassets\\css\\open-iconic"}, {"Id": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\ICON-LICENSE", "PackagePath": "staticwebassets\\css\\open-iconic"}, {"Id": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\README.md", "PackagePath": "staticwebassets\\css\\open-iconic\\README.md"}, {"Id": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\font\\css\\open-iconic-bootstrap.min.css", "PackagePath": "staticwebassets\\css\\open-iconic\\font\\css\\open-iconic-bootstrap.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.eot", "PackagePath": "staticwebassets\\css\\open-iconic\\font\\fonts\\open-iconic.eot"}, {"Id": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.otf", "PackagePath": "staticwebassets\\css\\open-iconic\\font\\fonts\\open-iconic.otf"}, {"Id": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.svg", "PackagePath": "staticwebassets\\css\\open-iconic\\font\\fonts\\open-iconic.svg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.ttf", "PackagePath": "staticwebassets\\css\\open-iconic\\font\\fonts\\open-iconic.ttf"}, {"Id": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.woff", "PackagePath": "staticwebassets\\css\\open-iconic\\font\\fonts\\open-iconic.woff"}, {"Id": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\site.css", "PackagePath": "staticwebassets\\css\\site.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\favicon.ico", "PackagePath": "staticwebassets\\favicon.ico"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.KnoarkLeadGen.Web.Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssetEndpoints.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.KnoarkLeadGen.Web.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.build.KnoarkLeadGen.Web.props", "PackagePath": "build\\KnoarkLeadGen.Web.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.buildMultiTargeting.KnoarkLeadGen.Web.props", "PackagePath": "buildMultiTargeting\\KnoarkLeadGen.Web.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.buildTransitive.KnoarkLeadGen.Web.props", "PackagePath": "buildTransitive\\KnoarkLeadGen.Web.props"}], "ElementsToRemove": []}