using KnoarkLeadGen.Data;
using MailKit.Net.Smtp;
using MailKit.Security;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MimeKit;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace KnoarkLeadGen.Services
{
    public class EmailService : IEmailService
    {
        private readonly ILogger<EmailService> _logger;
        private readonly IConfiguration _configuration;

        public EmailService(ILogger<EmailService> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
        }

        public async Task SendLeadEmailAsync(Lead lead)
        {
            if (string.IsNullOrEmpty(lead.Email))
            {
                _logger.LogWarning($"Cannot send email to lead {lead.Id}: No email address.");
                return;
            }

            try
            {
                var message = new MimeMessage();
                message.From.Add(new MailboxAddress("Knoark NavLink", _configuration["Email:From"]));
                message.To.Add(new MailboxAddress(lead.ContactName ?? lead.CompanyName, lead.Email));
                message.Subject = "Consultation Opportunity: Logistics and International Shipping Services";

                var body = $@"
                    Dear {lead.ContactName ?? "Valued Partner"},

                    I hope this email finds you well. My name is [Your Name] from Knoark NavLink, a global trade and logistics company specializing in seamless import/export services, customs handling, and supply chain management.

                    We noticed that {lead.CompanyName} operates in the {lead.Industry ?? "relevant industry"} sector and may benefit from our expertise in international logistics and shipping solutions.

                    We would love to schedule a brief consultation call to discuss how Knoark NavLink can support your logistics and international shipping needs. Our services include:

                    - Comprehensive import/export management
                    - Customs clearance and documentation
                    - Global supply chain optimization
                    - Competitive shipping rates

                    Would you be available for a 15-minute call next week? Please let me know your preferred time, or feel free to reply to this email.

                    Best regards,
                    [Your Name]
                    [Your Position]
                    Knoark NavLink
                    {_configuration["Email:From"]}
                    www.knoark.com
                    Phone: [Your Phone Number]

                    Note: If you no longer wish to receive these emails, please reply with 'unsubscribe'.
                    ";

                message.Body = new TextPart("plain") { Text = body };

                using var client = new SmtpClient();
                await client.ConnectAsync(_configuration["Email:Smtp:Host"], int.Parse(_configuration["Email:Smtp:Port"]), SecureSocketOptions.StartTls);
                await client.AuthenticateAsync(_configuration["Email:Smtp:Username"], _configuration["Email:Smtp:Password"]);
                await client.SendAsync(message);
                await client.DisconnectAsync(true);

                _logger.LogInformation($"Email sent successfully to {lead.Email} for lead {lead.Id}.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error sending email to {lead.Email} for lead {lead.Id}.");
                throw;
            }
        }

        public async Task SendCustomEmailAsync(string toEmail, string toName, string subject, string htmlBody)
        {
            if (string.IsNullOrEmpty(toEmail))
            {
                _logger.LogWarning("Cannot send email: No email address provided.");
                return;
            }

            try
            {
                var message = new MimeMessage();
                message.From.Add(new MailboxAddress("Knoark NavLink", _configuration["Email:From"]));
                message.To.Add(new MailboxAddress(toName, toEmail));
                message.Subject = subject;

                message.Body = new TextPart("html") { Text = htmlBody };

                using var client = new SmtpClient();
                await client.ConnectAsync(_configuration["Email:Smtp:Host"], int.Parse(_configuration["Email:Smtp:Port"]), SecureSocketOptions.StartTls);
                await client.AuthenticateAsync(_configuration["Email:Smtp:Username"], _configuration["Email:Smtp:Password"]);
                await client.SendAsync(message);
                await client.DisconnectAsync(true);

                _logger.LogInformation($"Custom email sent successfully to {toEmail}.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error sending custom email to {toEmail}.");
                throw;
            }
        }

        public async Task SendBulkEmailsAsync(List<(string Email, string Name)> recipients, string subject, string htmlBody)
        {
            foreach (var (email, name) in recipients)
            {
                var personalizedBody = htmlBody.Replace("{{Name}}", name);
                await SendCustomEmailAsync(email, name, subject, personalizedBody);
            }
        }
    }
}
