<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Blazored.TextEditor" Version="1.0.8" />
    <PackageReference Include="ClosedXML" Version="0.102.3" />
    <PackageReference Include="Hangfire.AspNetCore" Version="1.8.21" />
    <PackageReference Include="Hangfire.SqlServer" Version="1.8.21" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.10">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\KnoarkLeadGen.Data\KnoarkLeadGen.Data.csproj" />
    <ProjectReference Include="..\KnoarkLeadGen.Services\KnoarkLeadGen.Services.csproj" />
  </ItemGroup>

</Project>
