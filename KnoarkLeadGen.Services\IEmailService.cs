using KnoarkLeadGen.Data;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace KnoarkLeadGen.Services
{
    public interface IEmailService
    {
        Task SendLeadEmailAsync(Lead lead);
        Task SendCustomEmailAsync(string toEmail, string toName, string subject, string htmlBody);
        Task SendBulkEmailsAsync(List<(string Email, string Name)> recipients, string subject, string htmlBody);
    }
}
