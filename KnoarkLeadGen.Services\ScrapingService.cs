using HtmlAgilityPack;
using KnoarkLeadGen.Data;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Playwright;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace KnoarkLeadGen.Services
{
    public class ScrapingService : IScrapingService
    {
        private readonly ILogger<ScrapingService> _logger;
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;

        public ScrapingService(ILogger<ScrapingService> logger, HttpClient httpClient, IConfiguration configuration)
        {
            _logger = logger;
            _httpClient = httpClient;
            _configuration = configuration;
        }

        public async Task<List<Lead>> ScrapeLeadsAsync()
        {
            var leads = new List<Lead>();

            try
            {
                // Scrape logistics and shipping related businesses
                var logisticsLeads = await ScrapeLogisticsCompaniesAsync();
                leads.AddRange(logisticsLeads);

                // Scrape manufacturers and exporters (potential shippers)
                var manufacturerLeads = await ScrapeManufacturersAsync();
                leads.AddRange(manufacturerLeads);

                // Scrape wholesalers and distributors
                var wholesalerLeads = await ScrapeWholesalersAsync();
                leads.AddRange(wholesalerLeads);

                // Scrape e-commerce companies (online retailers needing shipping)
                var ecommerceLeads = await ScrapeEcommerceCompaniesAsync();
                leads.AddRange(ecommerceLeads);

                // Scrape import-export companies
                var importExportLeads = await ScrapeImportExportCompaniesAsync();
                leads.AddRange(importExportLeads);

                // Scrape from LinkedIn (logistics decision makers)
                var linkedinLeads = await ScrapeLinkedInLogisticsAsync();
                leads.AddRange(linkedinLeads);

                // Remove duplicates based on company name and website
                leads = leads.GroupBy(l => new { l.CompanyName, l.Website })
                            .Select(g => g.First())
                            .ToList();

                _logger.LogInformation($"Scraped {leads.Count} unique logistics/shipping leads successfully.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during scraping.");
            }

            return leads;
        }

        private async Task<List<Lead>> ScrapeLogisticsCompaniesAsync()
        {
            var leads = new List<Lead>();
            var baseUrls = new[]
            {
                "https://www.thomasnet.com/nsearch.html?cov=NA&heading=43990101", // Logistics Services
                "https://www.thomasnet.com/nsearch.html?cov=NA&heading=43990102", // Freight Forwarding
                "https://www.thomasnet.com/nsearch.html?cov=NA&heading=43990103", // Shipping Services
                "https://www.thomasnet.com/nsearch.html?cov=NA&heading=43990104", // Warehousing
                "https://www.thomasnet.com/nsearch.html?cov=NA&heading=43990105"  // Supply Chain Management
            };

            foreach (var baseUrl in baseUrls)
            {
                var currentUrl = baseUrl;
                var pageCount = 0;

                while (pageCount < 5) // Limit to 5 pages per category
                {
                    try
                    {
                        var response = await _httpClient.GetStringAsync(currentUrl);
                        var doc = new HtmlDocument();
                        doc.LoadHtml(response);

                        // Updated selectors based on Thomasnet structure
                        var companyNodes = doc.DocumentNode.SelectNodes("//div[contains(@class, 'company-card') or contains(@class, 'supplier-card')]");
                        if (companyNodes != null)
                        {
                            foreach (var node in companyNodes.Take(25)) // Limit per page
                            {
                                var companyName = node.SelectSingleNode(".//h2 | .//h3 | .//a[contains(@class, 'company-name')]")?.InnerText.Trim();
                                var website = node.SelectSingleNode(".//a[contains(@href, 'http') and not(contains(@href, 'thomasnet'))]")?.GetAttributeValue("href", "");
                                var industry = ExtractIndustryFromUrl(baseUrl);
                                var location = node.SelectSingleNode(".//span[contains(@class, 'location')] | .//div[contains(@class, 'address')]")?.InnerText.Trim();

                                if (!string.IsNullOrEmpty(companyName))
                                {
                                    var lead = new Lead
                                    {
                                        CompanyName = companyName,
                                        Website = website,
                                        Industry = industry,
                                        Location = location ?? "India/Global"
                                    };

                                    // Extract contact info from company website
                                    if (!string.IsNullOrEmpty(website))
                                    {
                                        var contactInfo = await ExtractContactInfoFromWebsiteAsync(website);
                                        if (contactInfo != null)
                                        {
                                            lead.ContactName = contactInfo.ContactName;
                                            lead.JobTitle = contactInfo.JobTitle;
                                            lead.Email = contactInfo.Email;
                                        }
                                    }

                                    leads.Add(lead);
                                }
                            }
                        }

                        // Find next page URL
                        var nextPageLink = doc.DocumentNode.SelectSingleNode("//a[contains(@class, 'next') or contains(text(), 'Next')]");
                        if (nextPageLink != null)
                        {
                            var nextUrl = nextPageLink.GetAttributeValue("href", "");
                            if (!string.IsNullOrEmpty(nextUrl))
                            {
                                currentUrl = nextUrl.StartsWith("http") ? nextUrl : "https://www.thomasnet.com" + nextUrl;
                                pageCount++;
                            }
                            else
                            {
                                break;
                            }
                        }
                        else
                        {
                            break;
                        }

                        // Add delay to be respectful
                        await Task.Delay(1000);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Error scraping logistics URL: {currentUrl}");
                        break;
                    }
                }
            }

            return leads;
        }

        private async Task<List<Lead>> ScrapeManufacturersAsync()
        {
            var leads = new List<Lead>();
            var urls = new[]
            {
                "https://www.thomasnet.com/products/manufacturing-1001",
                "https://www.thomasnet.com/products/textiles-1002",
                "https://www.thomasnet.com/products/electronics-1003",
                "https://www.thomasnet.com/products/automotive-parts-1004",
                "https://www.thomasnet.com/products/chemicals-1005"
            };

            foreach (var url in urls)
            {
                try
                {
                    var response = await _httpClient.GetStringAsync(url);
                    var doc = new HtmlDocument();
                    doc.LoadHtml(response);

                    // Updated selectors for manufacturers
                    var companyNodes = doc.DocumentNode.SelectNodes("//div[contains(@class, 'company-card') or contains(@class, 'supplier-card')]");
                    if (companyNodes != null)
                    {
                        foreach (var node in companyNodes.Take(25))
                        {
                            var companyName = node.SelectSingleNode(".//h2 | .//h3 | .//a[contains(@class, 'company-name')]")?.InnerText.Trim();
                            var website = node.SelectSingleNode(".//a[contains(@href, 'http') and not(contains(@href, 'thomasnet'))]")?.GetAttributeValue("href", "");
                            var industry = ExtractIndustryFromUrl(url);
                            var location = node.SelectSingleNode(".//span[contains(@class, 'location')] | .//div[contains(@class, 'address')]")?.InnerText.Trim();

                            if (!string.IsNullOrEmpty(companyName))
                            {
                                leads.Add(new Lead
                                {
                                    CompanyName = companyName,
                                    Website = website,
                                    Industry = industry,
                                    Location = location ?? "India/Global",
                                    Notes = "Potential shipper - manufacturer/exporter"
                                });
                            }
                        }
                    }

                    await Task.Delay(1000);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Error scraping manufacturers URL: {url}");
                }
            }

            return leads;
        }

        private async Task<List<Lead>> ScrapeImportExportCompaniesAsync()
        {
            var leads = new List<Lead>();
            var urls = new[]
            {
                "https://www.thomasnet.com/products/import-export-services-1001",
                "https://www.thomasnet.com/products/trading-companies-1002",
                "https://www.thomasnet.com/products/international-trade-1003"
            };

            foreach (var url in urls)
            {
                try
                {
                    var response = await _httpClient.GetStringAsync(url);
                    var doc = new HtmlDocument();
                    doc.LoadHtml(response);

                    var companyNodes = doc.DocumentNode.SelectNodes("//div[contains(@class, 'company-card') or contains(@class, 'supplier-card')]");
                    if (companyNodes != null)
                    {
                        foreach (var node in companyNodes.Take(25))
                        {
                            var companyName = node.SelectSingleNode(".//h2 | .//h3 | .//a[contains(@class, 'company-name')]")?.InnerText.Trim();
                            var website = node.SelectSingleNode(".//a[contains(@href, 'http') and not(contains(@href, 'thomasnet'))]")?.GetAttributeValue("href", "");
                            var industry = ExtractIndustryFromUrl(url);
                            var location = node.SelectSingleNode(".//span[contains(@class, 'location')] | .//div[contains(@class, 'address')]")?.InnerText.Trim();

                            if (!string.IsNullOrEmpty(companyName))
                            {
                                leads.Add(new Lead
                                {
                                    CompanyName = companyName,
                                    Website = website,
                                    Industry = industry,
                                    Location = location ?? "India/Global",
                                    Notes = "Import-export company - potential logistics partner"
                                });
                            }
                        }
                    }

                    await Task.Delay(1000);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Error scraping import-export companies URL: {url}");
                }
            }

            return leads;
        }

        private async Task<List<Lead>> ScrapeLinkedInLogisticsAsync()
        {
            var leads = new List<Lead>();

            try
            {
                // Initialize Playwright
                using var playwright = await Playwright.CreateAsync();
                await using var browser = await playwright.Chromium.LaunchAsync(new BrowserTypeLaunchOptions
                {
                    Headless = true,
                    Args = new[] { "--no-sandbox", "--disable-setuid-sandbox" }
                });
                var page = await browser.NewPageAsync();

                // Navigate to LinkedIn login
                await page.GotoAsync("https://www.linkedin.com/login");

                // Get credentials from secrets.json
                var username = _configuration["LinkedIn:Email"];
                var password = _configuration["LinkedIn:Password"];

                if (!string.IsNullOrEmpty(username) && !string.IsNullOrEmpty(password))
                {
                    // Fill login form
                    await page.FillAsync("#username", username);
                    await page.FillAsync("#password", password);
                    await page.ClickAsync("button[type='submit']");

                    // Wait for navigation
                    await page.WaitForURLAsync("**/feed/**");

                    // Navigate to Sales Navigator
                    await page.GotoAsync("https://www.linkedin.com/sales/search/people");

                    // Search for logistics and shipping decision makers in India
                    var searchTerms = new[] {
                        "Logistics Manager India",
                        "Supply Chain Director India",
                        "Export Manager India",
                        "Shipping Manager India",
                        "Freight Forwarder India",
                        "Import Export Manager India"
                    };

                    foreach (var term in searchTerms)
                    {
                        await page.FillAsync("input[placeholder*='Search']", term);
                        await page.Keyboard.PressAsync("Enter");

                        // Wait for results
                        await page.WaitForSelectorAsync(".search-results__list");

                        // Extract leads from results
                        var resultNodes = await page.QuerySelectorAllAsync(".search-results__result-item");
                        foreach (var node in resultNodes.Take(15))
                        {
                            var nameElement = await node.QuerySelectorAsync(".result-lockup__name");
                            var name = nameElement != null ? await nameElement.InnerTextAsync() : null;

                            var titleElement = await node.QuerySelectorAsync(".result-lockup__highlight-keyword");
                            var title = titleElement != null ? await titleElement.InnerTextAsync() : null;

                            var companyElement = await node.QuerySelectorAsync(".result-lockup__position-company");
                            var company = companyElement != null ? await companyElement.InnerTextAsync() : null;

                            if (!string.IsNullOrEmpty(name) && !string.IsNullOrEmpty(company))
                            {
                                leads.Add(new Lead
                                {
                                    CompanyName = company,
                                    ContactName = name,
                                    JobTitle = title,
                                    Industry = "Logistics/Shipping",
                                    Location = "India",
                                    Notes = "Logistics decision maker - potential client"
                                });
                            }
                        }

                        await Task.Delay(2000); // Respectful delay
                    }
                }
                else
                {
                    _logger.LogWarning("LinkedIn scraping skipped - credentials not configured in secrets.json.");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error scraping LinkedIn for logistics contacts.");
            }

            return leads;
        }

        private async Task<List<Lead>> ScrapeWholesalersAsync()
        {
            var leads = new List<Lead>();
            var urls = new[]
            {
                "https://www.thomasnet.com/products/wholesale-trade-1001",
                "https://www.thomasnet.com/products/distributors-1002",
                "https://www.thomasnet.com/products/suppliers-1003"
            };

            foreach (var url in urls)
            {
                try
                {
                    var response = await _httpClient.GetStringAsync(url);
                    var doc = new HtmlDocument();
                    doc.LoadHtml(response);

                    var companyNodes = doc.DocumentNode.SelectNodes("//div[contains(@class, 'company-card') or contains(@class, 'supplier-card')]");
                    if (companyNodes != null)
                    {
                        foreach (var node in companyNodes.Take(25))
                        {
                            var companyName = node.SelectSingleNode(".//h2 | .//h3 | .//a[contains(@class, 'company-name')]")?.InnerText.Trim();
                            var website = node.SelectSingleNode(".//a[contains(@href, 'http') and not(contains(@href, 'thomasnet'))]")?.GetAttributeValue("href", "");
                            var industry = ExtractIndustryFromUrl(url);
                            var location = node.SelectSingleNode(".//span[contains(@class, 'location')] | .//div[contains(@class, 'address')]")?.InnerText.Trim();

                            if (!string.IsNullOrEmpty(companyName))
                            {
                                leads.Add(new Lead
                                {
                                    CompanyName = companyName,
                                    Website = website,
                                    Industry = industry,
                                    Location = location ?? "India/Global",
                                    Notes = "Potential shipper - wholesaler/distributor"
                                });
                            }
                        }
                    }

                    await Task.Delay(1000);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Error scraping wholesalers URL: {url}");
                }
            }

            return leads;
        }

        private async Task<List<Lead>> ScrapeEcommerceCompaniesAsync()
        {
            var leads = new List<Lead>();

            // Use Google Custom Search API instead of direct scraping
            var apiKey = _configuration["GoogleCustomSearch:ApiKey"];
            var searchEngineId = _configuration["GoogleCustomSearch:SearchEngineId"];

            if (string.IsNullOrEmpty(apiKey) || string.IsNullOrEmpty(searchEngineId))
            {
                _logger.LogWarning("Google Custom Search API credentials not configured. Skipping e-commerce scraping.");
                return leads;
            }

            var searchQueries = new[]
            {
                "indian manufacturer site:myshopify.com",
                "indian exporter site:woocommerce.com",
                "indian online retailers wholesale",
                "indian ecommerce companies shipping"
            };

            foreach (var query in searchQueries)
            {
                try
                {
                    var apiUrl = $"https://www.googleapis.com/customsearch/v1?key={apiKey}&cx={searchEngineId}&q={Uri.EscapeDataString(query)}&num=10";

                    var response = await _httpClient.GetStringAsync(apiUrl);
                    var searchResults = System.Text.Json.JsonDocument.Parse(response);

                    if (searchResults.RootElement.TryGetProperty("items", out var items))
                    {
                        foreach (var item in items.EnumerateArray())
                        {
                            var title = item.GetProperty("title").GetString();
                            var link = item.GetProperty("link").GetString();

                            if (!string.IsNullOrEmpty(link) && (link.Contains("myshopify.com") || link.Contains("woocommerce.com") || link.Contains("bigcommerce.com")))
                            {
                                var lead = new Lead
                                {
                                    CompanyName = title ?? "E-commerce Store",
                                    Website = link,
                                    Industry = "E-commerce",
                                    Location = "India",
                                    Notes = "Potential shipper - online retailer needing logistics"
                                };

                                // Extract contact info from company website
                                var contactInfo = await ExtractContactInfoFromWebsiteAsync(link);
                                if (contactInfo != null)
                                {
                                    lead.ContactName = contactInfo.ContactName;
                                    lead.JobTitle = contactInfo.JobTitle;
                                    lead.Email = contactInfo.Email;
                                }

                                leads.Add(lead);
                            }
                        }
                    }

                    await Task.Delay(1000); // Respectful delay
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Error scraping e-commerce companies with query: {query}");
                }
            }

            return leads;
        }

        private string ExtractIndustryFromUrl(string url)
        {
            if (url.Contains("logistics-services")) return "Logistics Services";
            if (url.Contains("freight-forwarding")) return "Freight Forwarding";
            if (url.Contains("shipping-services")) return "Shipping Services";
            if (url.Contains("warehousing")) return "Warehousing";
            if (url.Contains("supply-chain-management")) return "Supply Chain Management";
            if (url.Contains("manufacturing")) return "Manufacturing";
            if (url.Contains("textiles")) return "Textiles";
            if (url.Contains("electronics")) return "Electronics";
            if (url.Contains("automotive-parts")) return "Automotive Parts";
            if (url.Contains("chemicals")) return "Chemicals";
            if (url.Contains("wholesale-trade")) return "Wholesale Trade";
            if (url.Contains("distributors")) return "Distribution";
            if (url.Contains("suppliers")) return "Suppliers";
            if (url.Contains("import-export-services")) return "Import/Export Services";
            if (url.Contains("trading-companies")) return "Trading Companies";
            if (url.Contains("international-trade")) return "International Trade";
            return "General Business";
        }

        private async Task<ContactInfo> ExtractContactInfoFromWebsiteAsync(string websiteUrl)
        {
            try
            {
                var response = await _httpClient.GetStringAsync(websiteUrl);
                var doc = new HtmlDocument();
                doc.LoadHtml(response);

                // Extract email from various patterns
                var emailRegex = new Regex(@"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b");
                var emailMatch = emailRegex.Match(response);
                var email = emailMatch.Success ? emailMatch.Value : null;

                // Extract contact name from common selectors
                var contactNameSelectors = new[] {
                    "//h1[contains(text(), 'Contact')]/following-sibling::*//text()",
                    "//div[contains(@class, 'contact')]//h2 | //div[contains(@class, 'contact')]//h3",
                    "//span[contains(@class, 'name')]",
                    "//div[contains(@class, 'person')]"
                };

                string contactName = null;
                foreach (var selector in contactNameSelectors)
                {
                    var node = doc.DocumentNode.SelectSingleNode(selector);
                    if (node != null)
                    {
                        contactName = node.InnerText.Trim();
                        if (!string.IsNullOrEmpty(contactName)) break;
                    }
                }

                // Extract job title
                var titleSelectors = new[] {
                    "//span[contains(@class, 'title')]",
                    "//div[contains(@class, 'position')]",
                    "//p[contains(@class, 'role')]"
                };

                string jobTitle = null;
                foreach (var selector in titleSelectors)
                {
                    var node = doc.DocumentNode.SelectSingleNode(selector);
                    if (node != null)
                    {
                        jobTitle = node.InnerText.Trim();
                        if (!string.IsNullOrEmpty(jobTitle)) break;
                    }
                }

                if (!string.IsNullOrEmpty(email) || !string.IsNullOrEmpty(contactName))
                {
                    return new ContactInfo
                    {
                        ContactName = contactName,
                        JobTitle = jobTitle,
                        Email = email
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, $"Error extracting contact info from {websiteUrl}");
            }

            return null;
        }

        private class ContactInfo
        {
            public string ContactName { get; set; }
            public string JobTitle { get; set; }
            public string Email { get; set; }
        }
    }
}
