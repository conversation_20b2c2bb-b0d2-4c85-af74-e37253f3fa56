{"Version": 1, "Hash": "v87UU2WFkkUp6t8RBmyiqO2NannmWy9+RIO1d4hPtd4=", "Source": "KnoarkLeadGen.Web", "BasePath": "_content/KnoarkLeadGen.Web", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "KnoarkLeadGen.Web\\wwwroot", "Source": "KnoarkLeadGen.Web", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\", "BasePath": "_content/KnoarkLeadGen.Web", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazored.texteditor\\1.0.8\\staticwebassets\\Blazored-BlazorQuill.js", "SourceId": "Blazored.TextEditor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazored.texteditor\\1.0.8\\staticwebassets\\", "BasePath": "_content/Blazored.TextEditor", "RelativePath": "Blazored-BlazorQuill.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "jhc6a5qxc6", "Integrity": "SR20yxYPO0H+vfa5kOfBkCHUICefwXdMdP7uhOMZ4dM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazored.texteditor\\1.0.8\\staticwebassets\\Blazored-BlazorQuill.js"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazored.texteditor\\1.0.8\\staticwebassets\\quill-blot-formatter.min.js", "SourceId": "Blazored.TextEditor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazored.texteditor\\1.0.8\\staticwebassets\\", "BasePath": "_content/Blazored.TextEditor", "RelativePath": "quill-blot-formatter.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "06wjp<PERSON><PERSON>s", "Integrity": "aNk36zfXB+ixZlkufdUvWaoJNMMNVB0nqx7xVe0YdAA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazored.texteditor\\1.0.8\\staticwebassets\\quill-blot-formatter.min.js"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\obj\\Debug\\net8.0\\scopedcss\\bundle\\KnoarkLeadGen.Web.styles.css", "SourceId": "KnoarkLeadGen.Web", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\obj\\Debug\\net8.0\\scopedcss\\bundle\\", "BasePath": "_content/KnoarkLeadGen.Web", "RelativePath": "KnoarkLeadGen.Web#[.{fingerprint}]?.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "Fingerprint": "gdj14ul9k4", "Integrity": "rJs4xJZcuSH+dxNv2SqpeHROrEDXpd3pDNgXZuw1NcA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\obj\\Debug\\net8.0\\scopedcss\\bundle\\KnoarkLeadGen.Web.styles.css"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\KnoarkLeadGen.Web.bundle.scp.css", "SourceId": "KnoarkLeadGen.Web", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\", "BasePath": "_content/KnoarkLeadGen.Web", "RelativePath": "KnoarkLeadGen.Web#[.{fingerprint}]!.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "Fingerprint": "gdj14ul9k4", "Integrity": "rJs4xJZcuSH+dxNv2SqpeHROrEDXpd3pDNgXZuw1NcA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\KnoarkLeadGen.Web.bundle.scp.css"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "SourceId": "KnoarkLeadGen.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\", "BasePath": "_content/KnoarkLeadGen.Web", "RelativePath": "css/bootstrap/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bpk8xqwxhs", "Integrity": "z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\bootstrap\\bootstrap.min.css"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "SourceId": "KnoarkLeadGen.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\", "BasePath": "_content/KnoarkLeadGen.Web", "RelativePath": "css/bootstrap/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\bootstrap\\bootstrap.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\FONT-LICENSE", "SourceId": "KnoarkLeadGen.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\", "BasePath": "_content/KnoarkLeadGen.Web", "RelativePath": "css/open-iconic/FONT-LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "48tmkg660f", "Integrity": "jA4J4h/k76zVxbFKEaWwFKJccmO0voOQ1DbUW+5YNlI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\FONT-LICENSE"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\font\\css\\open-iconic-bootstrap.min.css", "SourceId": "KnoarkLeadGen.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\", "BasePath": "_content/KnoarkLeadGen.Web", "RelativePath": "css/open-iconic/font/css/open-iconic-bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "cmapd0fi15", "Integrity": "BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\font\\css\\open-iconic-bootstrap.min.css"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.eot", "SourceId": "KnoarkLeadGen.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\", "BasePath": "_content/KnoarkLeadGen.Web", "RelativePath": "css/open-iconic/font/fonts/open-iconic#[.{fingerprint}]?.eot", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "0uw8dim9nl", "Integrity": "OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.eot"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.otf", "SourceId": "KnoarkLeadGen.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\", "BasePath": "_content/KnoarkLeadGen.Web", "RelativePath": "css/open-iconic/font/fonts/open-iconic#[.{fingerprint}]?.otf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "wk8x8xm0ah", "Integrity": "sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.otf"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.svg", "SourceId": "KnoarkLeadGen.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\", "BasePath": "_content/KnoarkLeadGen.Web", "RelativePath": "css/open-iconic/font/fonts/open-iconic#[.{fingerprint}]?.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "sjnzgf7e1h", "Integrity": "+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.svg"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.ttf", "SourceId": "KnoarkLeadGen.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\", "BasePath": "_content/KnoarkLeadGen.Web", "RelativePath": "css/open-iconic/font/fonts/open-iconic#[.{fingerprint}]?.ttf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ll5grcv8wv", "Integrity": "p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.ttf"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.woff", "SourceId": "KnoarkLeadGen.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\", "BasePath": "_content/KnoarkLeadGen.Web", "RelativePath": "css/open-iconic/font/fonts/open-iconic#[.{fingerprint}]?.woff", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "h4d0pazwgy", "Integrity": "cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.woff"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\ICON-LICENSE", "SourceId": "KnoarkLeadGen.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\", "BasePath": "_content/KnoarkLeadGen.Web", "RelativePath": "css/open-iconic/ICON-LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4dwjve0o0b", "Integrity": "aF5g/izareSj02F3MPSoTGNbcMBl9nmZKDe04zjU/ss=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\ICON-LICENSE"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\README.md", "SourceId": "KnoarkLeadGen.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\", "BasePath": "_content/KnoarkLeadGen.Web", "RelativePath": "css/open-iconic/README#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "8h4oiah9s0", "Integrity": "rDb1fXbrDo8/dpt6Gi3UAobONVQv/lE2bB7lGwRQ0jM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\README.md"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\site.css", "SourceId": "KnoarkLeadGen.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\", "BasePath": "_content/KnoarkLeadGen.Web", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "1fpfb6x0uo", "Integrity": "bQF09ZZOsk0T2q2MpSrJMbqdu0Ks9Ea03LFI7wJyLeU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\favicon.ico", "SourceId": "KnoarkLeadGen.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\", "BasePath": "_content/KnoarkLeadGen.Web", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico"}], "Endpoints": [{"Route": "_content/Blazored.TextEditor/Blazored-BlazorQuill.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazored.texteditor\\1.0.8\\staticwebassets\\Blazored-BlazorQuill.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2684"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"SR20yxYPO0H+vfa5kOfBkCHUICefwXdMdP7uhOMZ4dM=\""}, {"Name": "Last-Modified", "Value": "Wed, 04 May 2022 18:02:08 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SR20yxYPO0H+vfa5kOfBkCHUICefwXdMdP7uhOMZ4dM="}]}, {"Route": "_content/Blazored.TextEditor/quill-blot-formatter.min.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazored.texteditor\\1.0.8\\staticwebassets\\quill-blot-formatter.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30284"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"aNk36zfXB+ixZlkufdUvWaoJNMMNVB0nqx7xVe0YdAA=\""}, {"Name": "Last-Modified", "Value": "Wed, 04 May 2022 18:02:08 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aNk36zfXB+ixZlkufdUvWaoJNMMNVB0nqx7xVe0YdAA="}]}, {"Route": "css/bootstrap/bootstrap.min.bpk8xqwxhs.css", "AssetFile": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Thu, 23 Oct 2025 09:17:08 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bpk8xqwxhs"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "css/bootstrap/bootstrap.min.css", "AssetFile": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Thu, 23 Oct 2025 09:17:08 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "css/bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 23 Oct 2025 09:17:08 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.map"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/bootstrap/bootstrap.min.css.map", "AssetFile": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 23 Oct 2025 09:17:08 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/open-iconic/FONT-LICENSE", "AssetFile": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\FONT-LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4103"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"jA4J4h/k76zVxbFKEaWwFKJccmO0voOQ1DbUW+5YNlI=\""}, {"Name": "Last-Modified", "Value": "Thu, 23 Oct 2025 09:17:08 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jA4J4h/k76zVxbFKEaWwFKJccmO0voOQ1DbUW+5YNlI="}]}, {"Route": "css/open-iconic/FONT-LICENSE.48tmkg660f", "AssetFile": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\FONT-LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4103"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"jA4J4h/k76zVxbFKEaWwFKJccmO0voOQ1DbUW+5YNlI=\""}, {"Name": "Last-Modified", "Value": "Thu, 23 Oct 2025 09:17:08 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "48tmkg660f"}, {"Name": "label", "Value": "css/open-iconic/FONT-LICENSE"}, {"Name": "integrity", "Value": "sha256-jA4J4h/k76zVxbFKEaWwFKJccmO0voOQ1DbUW+5YNlI="}]}, {"Route": "css/open-iconic/font/css/open-iconic-bootstrap.min.cmapd0fi15.css", "AssetFile": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\font\\css\\open-iconic-bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9395"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 23 Oct 2025 09:17:08 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cmapd0fi15"}, {"Name": "label", "Value": "css/open-iconic/font/css/open-iconic-bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ="}]}, {"Route": "css/open-iconic/font/css/open-iconic-bootstrap.min.css", "AssetFile": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\font\\css\\open-iconic-bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9395"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 23 Oct 2025 09:17:08 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.0uw8dim9nl.eot", "AssetFile": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.eot", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28196"}, {"Name": "Content-Type", "Value": "application/vnd.ms-fontobject"}, {"Name": "ETag", "Value": "\"OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc=\""}, {"Name": "Last-Modified", "Value": "Thu, 23 Oct 2025 09:17:08 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0uw8dim9nl"}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.eot"}, {"Name": "integrity", "Value": "sha256-OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.eot", "AssetFile": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.eot", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28196"}, {"Name": "Content-Type", "Value": "application/vnd.ms-fontobject"}, {"Name": "ETag", "Value": "\"OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc=\""}, {"Name": "Last-Modified", "Value": "Thu, 23 Oct 2025 09:17:08 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.h4d0pazwgy.woff", "AssetFile": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14984"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI=\""}, {"Name": "Last-Modified", "Value": "Thu, 23 Oct 2025 09:17:08 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h4d0pazwgy"}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.woff"}, {"Name": "integrity", "Value": "sha256-cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.ll5grcv8wv.ttf", "AssetFile": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28028"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w=\""}, {"Name": "Last-Modified", "Value": "Thu, 23 Oct 2025 09:17:08 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ll5grcv8wv"}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.ttf"}, {"Name": "integrity", "Value": "sha256-p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.otf", "AssetFile": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.otf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20996"}, {"Name": "Content-Type", "Value": "font/otf"}, {"Name": "ETag", "Value": "\"sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0=\""}, {"Name": "Last-Modified", "Value": "Thu, 23 Oct 2025 09:17:08 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.sjnzgf7e1h.svg", "AssetFile": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "55332"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk=\""}, {"Name": "Last-Modified", "Value": "Thu, 23 Oct 2025 09:17:08 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sjnzgf7e1h"}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.svg"}, {"Name": "integrity", "Value": "sha256-+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.svg", "AssetFile": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "55332"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk=\""}, {"Name": "Last-Modified", "Value": "Thu, 23 Oct 2025 09:17:08 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.ttf", "AssetFile": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28028"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w=\""}, {"Name": "Last-Modified", "Value": "Thu, 23 Oct 2025 09:17:08 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.wk8x8xm0ah.otf", "AssetFile": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.otf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20996"}, {"Name": "Content-Type", "Value": "font/otf"}, {"Name": "ETag", "Value": "\"sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0=\""}, {"Name": "Last-Modified", "Value": "Thu, 23 Oct 2025 09:17:08 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wk8x8xm0ah"}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.otf"}, {"Name": "integrity", "Value": "sha256-sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.woff", "AssetFile": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14984"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI=\""}, {"Name": "Last-Modified", "Value": "Thu, 23 Oct 2025 09:17:08 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI="}]}, {"Route": "css/open-iconic/ICON-LICENSE", "AssetFile": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\ICON-LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1093"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"aF5g/izareSj02F3MPSoTGNbcMBl9nmZKDe04zjU/ss=\""}, {"Name": "Last-Modified", "Value": "Thu, 23 Oct 2025 09:17:08 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aF5g/izareSj02F3MPSoTGNbcMBl9nmZKDe04zjU/ss="}]}, {"Route": "css/open-iconic/ICON-LICENSE.4dwjve0o0b", "AssetFile": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\ICON-LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1093"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"aF5g/izareSj02F3MPSoTGNbcMBl9nmZKDe04zjU/ss=\""}, {"Name": "Last-Modified", "Value": "Thu, 23 Oct 2025 09:17:08 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4dwjve0o0b"}, {"Name": "label", "Value": "css/open-iconic/ICON-LICENSE"}, {"Name": "integrity", "Value": "sha256-aF5g/izareSj02F3MPSoTGNbcMBl9nmZKDe04zjU/ss="}]}, {"Route": "css/open-iconic/README.8h4oiah9s0.md", "AssetFile": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\README.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3658"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"rDb1fXbrDo8/dpt6Gi3UAobONVQv/lE2bB7lGwRQ0jM=\""}, {"Name": "Last-Modified", "Value": "Thu, 23 Oct 2025 09:17:08 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8h4oiah9s0"}, {"Name": "label", "Value": "css/open-iconic/README.md"}, {"Name": "integrity", "Value": "sha256-rDb1fXbrDo8/dpt6Gi3UAobONVQv/lE2bB7lGwRQ0jM="}]}, {"Route": "css/open-iconic/README.md", "AssetFile": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\open-iconic\\README.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3658"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"rDb1fXbrDo8/dpt6Gi3UAobONVQv/lE2bB7lGwRQ0jM=\""}, {"Name": "Last-Modified", "Value": "Thu, 23 Oct 2025 09:17:08 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rDb1fXbrDo8/dpt6Gi3UAobONVQv/lE2bB7lGwRQ0jM="}]}, {"Route": "css/site.1fpfb6x0uo.css", "AssetFile": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2810"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bQF09ZZOsk0T2q2MpSrJMbqdu0Ks9Ea03LFI7wJyLeU=\""}, {"Name": "Last-Modified", "Value": "Thu, 23 Oct 2025 09:17:08 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1fpfb6x0uo"}, {"Name": "label", "Value": "css/site.css"}, {"Name": "integrity", "Value": "sha256-bQF09ZZOsk0T2q2MpSrJMbqdu0Ks9Ea03LFI7wJyLeU="}]}, {"Route": "css/site.css", "AssetFile": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\css\\site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2810"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bQF09ZZOsk0T2q2MpSrJMbqdu0Ks9Ea03LFI7wJyLeU=\""}, {"Name": "Last-Modified", "Value": "Thu, 23 Oct 2025 09:17:08 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bQF09ZZOsk0T2q2MpSrJMbqdu0Ks9Ea03LFI7wJyLeU="}]}, {"Route": "favicon.61n19gt1b8.ico", "AssetFile": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5430"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "Thu, 23 Oct 2025 09:17:08 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "61n19gt1b8"}, {"Name": "label", "Value": "favicon.ico"}, {"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]}, {"Route": "favicon.ico", "AssetFile": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\wwwroot\\favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5430"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "Thu, 23 Oct 2025 09:17:08 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]}, {"Route": "KnoarkLeadGen.Web.bundle.scp.css", "AssetFile": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\KnoarkLeadGen.Web.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2855"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rJs4xJZcuSH+dxNv2SqpeHROrEDXpd3pDNgXZuw1NcA=\""}, {"Name": "Last-Modified", "Value": "Thu, 23 Oct 2025 11:06:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rJs4xJZcuSH+dxNv2SqpeHROrEDXpd3pDNgXZuw1NcA="}]}, {"Route": "KnoarkLeadGen.Web.gdj14ul9k4.bundle.scp.css", "AssetFile": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\KnoarkLeadGen.Web.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2855"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rJs4xJZcuSH+dxNv2SqpeHROrEDXpd3pDNgXZuw1NcA=\""}, {"Name": "Last-Modified", "Value": "Thu, 23 Oct 2025 11:06:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gdj14ul9k4"}, {"Name": "label", "Value": "KnoarkLeadGen.Web.bundle.scp.css"}, {"Name": "integrity", "Value": "sha256-rJs4xJZcuSH+dxNv2SqpeHROrEDXpd3pDNgXZuw1NcA="}]}, {"Route": "KnoarkLeadGen.Web.gdj14ul9k4.styles.css", "AssetFile": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\obj\\Debug\\net8.0\\scopedcss\\bundle\\KnoarkLeadGen.Web.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2855"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rJs4xJZcuSH+dxNv2SqpeHROrEDXpd3pDNgXZuw1NcA=\""}, {"Name": "Last-Modified", "Value": "Thu, 23 Oct 2025 11:06:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gdj14ul9k4"}, {"Name": "label", "Value": "KnoarkLeadGen.Web.styles.css"}, {"Name": "integrity", "Value": "sha256-rJs4xJZcuSH+dxNv2SqpeHROrEDXpd3pDNgXZuw1NcA="}]}, {"Route": "KnoarkLeadGen.Web.styles.css", "AssetFile": "C:\\Users\\<USER>\\Desktop\\KnoarkLeadGen\\KnoarkLeadGen.Web\\obj\\Debug\\net8.0\\scopedcss\\bundle\\KnoarkLeadGen.Web.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2855"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rJs4xJZcuSH+dxNv2SqpeHROrEDXpd3pDNgXZuw1NcA=\""}, {"Name": "Last-Modified", "Value": "Thu, 23 Oct 2025 11:06:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rJs4xJZcuSH+dxNv2SqpeHROrEDXpd3pDNgXZuw1NcA="}]}]}