.navbar-toggler[b-7hcxl2cu8f] {
    background-color: rgba(255, 255, 255, 0.1);
}

.top-row[b-7hcxl2cu8f] {
    height: 3.5rem;
    background-color: rgba(0,0,0,0.4);
}

.navbar-brand[b-7hcxl2cu8f] {
    font-size: 1.1rem;
}

.oi[b-7hcxl2cu8f] {
    width: 2rem;
    font-size: 1.1rem;
    vertical-align: text-top;
    top: -2px;
}

.nav-item[b-7hcxl2cu8f] {
    font-size: 0.9rem;
    padding-bottom: 0.5rem;
}

    .nav-item:first-of-type[b-7hcxl2cu8f] {
        padding-top: 1rem;
    }

    .nav-item:last-of-type[b-7hcxl2cu8f] {
        padding-bottom: 1rem;
    }

    .nav-item[b-7hcxl2cu8f]  a {
        color: #d7d7d7;
        border-radius: 4px;
        height: 3rem;
        display: flex;
        align-items: center;
        line-height: 3rem;
    }

.nav-item[b-7hcxl2cu8f]  a.active {
    background-color: rgba(255,255,255,0.25);
    color: white;
}

.nav-item[b-7hcxl2cu8f]  a:hover {
    background-color: rgba(255,255,255,0.1);
    color: white;
}

@media (min-width: 641px) {
    .navbar-toggler[b-7hcxl2cu8f] {
        display: none;
    }

    .collapse[b-7hcxl2cu8f] {
        /* Never collapse the sidebar for wide screens */
        display: block;
    }
}
